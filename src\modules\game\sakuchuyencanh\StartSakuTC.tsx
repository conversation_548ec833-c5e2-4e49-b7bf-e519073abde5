import {
  <PERSON><PERSON>,
  FlatList,
  Image,
  ImageBackground,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import CountBadge from '../components/CountQuestions';
import {BottomGame} from '../components/BottomGame';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {CardText} from '../components/CardText';
import {useEffect, useRef, useState} from 'react';
import {Answer} from '../../../redux/reducers/game/sakuTcReducer';
import {PanGestureHandler} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import React from 'react';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSakuTcHook} from '../../../redux/hook/game/sakuTcHook';
import GameOverModal from '../components/GameOverModel';
import HintModel from '../components/HintModel';
import ModelConfirm from '../components/ModelConfirm';
import ModelPauseGame from '../components/ModelPauseGame';
import {checkPositionOrder} from '../utils/functions';

interface DropZone {
  id: string;
  answer: Answer | null;
  index: number;
  showZone?: boolean;
  x: number;
  y: number;
  height: number;
  width: number;
}

const StartSakuTC = () => {
  const sakuTcHook = useSakuTcHook();
  const gameHook = useGameHook();
  const {currentQuestion, questionDone, totalQuestion, listQuestion} =
    useSelector((state: RootState) => state.SakuTCStore);
  const {isGameOver, messageGameOver, gem, cup} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const [listZone, setListZone] = useState<DropZone[]>([]);
  const [currentDropZone, setCurrentDropZone] = useState<DropZone | null>(null);
  const [listAnswer, setListAnswer] = useState<Answer[]>([]);
  const [isShowModelConfirm, setIsShowModelConfirm] = useState<boolean>(false);
  const [isShowHintModel, setIsShowHintModel] = useState<boolean>(false);
  const [isPauseGame, setIsPauseGame] = useState<boolean>(false);

  const refDropZone = useRef<View>(null);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    startGame();
    return () => {
      refDropZone.current = null;
    };
  }, []);

  useEffect(() => {
    setListAnswer(currentQuestion.answers);
  }, [currentQuestion]);

  const startGame = () => {
    sakuTcHook.startGame();
    gameHook.restartGame();
    setListAnswer(currentQuestion.answers);
    const zones = currentQuestion.answers.map((item, index) => {
      return {
        id: Math.random().toString(),
        answer: null,
        index,
        x: 0,
        y: 0,
        height: 0,
        width: 0,
      };
    });
    const updatedZones = findAndUpdateHighestIndexItem(zones);
    setListZone(updatedZones);
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({animated: true});
    }, 500);
  };

  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  const onPauseGame = () => {
    gameHook.pauseGame();
    setIsPauseGame(true);
  };

  const onContinueGame = () => {
    gameHook.continueGame();
    setIsPauseGame(false);
  };

  const onShowModelConfirm = () => {
    setIsShowModelConfirm(true);
  };

  const onShowHintModel = () => {
    gameHook.setData({stateName: 'gem', value: gem - 10});
    setIsShowHintModel(true);
  };

  // Kiểm tra kết quả
  const checkAnswer = () => {
    const listAnswer = listZone.map(zone => zone.answer);
    const isCorrect = checkPositionOrder(
      listAnswer.filter(zone => zone !== null),
    );
    if (isCorrect) {
      Alert.alert('Correct');
    } else {
      Alert.alert('Wrong');
    }
  };

  // tìm và cập nhật item có index cao nhất
  const findAndUpdateHighestIndexItem = (zones: DropZone[]) => {
    const highestIndexItem = zones
      .filter(zone => zone.answer === null)
      .sort((a, b) => b.index - a.index)[0];

    if (highestIndexItem) {
      setCurrentDropZone(highestIndexItem);
      return zones.map(zone => ({
        ...zone,
        showZone: zone.id === highestIndexItem.id,
      }));
    }
    return zones;
  };

  // cập nhật danh sách drop zone
  const updateListZones = (zone: DropZone) => {
    const listZoneClone = [...listZone];
    const index = listZoneClone.findIndex(z => z.id === zone.id);
    listZoneClone[index] = zone;
    return listZoneClone;
  };

  // xoá answer khỏi list
  const removeAnswerFromList = (answer: Answer) => {
    const listZoneClone = [...listAnswer];
    const index = listZoneClone.findIndex(z => z.id === answer.id);
    listZoneClone.splice(index, 1);
    setListAnswer(listZoneClone);
    sakuTcHook.setData({stateName: 'currentQuestion', value: currentQuestion});
  };

  // thêm answer vào drop zone
  const addWordToDropZone = (answer: Answer) => {
    if (currentDropZone) {
      currentDropZone.answer = answer;
      let updatedZones = updateListZones(currentDropZone);
      updatedZones = findAndUpdateHighestIndexItem(updatedZones);
      setListZone(updatedZones);
      removeAnswerFromList(answer);
      if (currentDropZone.index < 1) {
        checkAnswer();
      }
    }
  };

  // render answer
  const DraggableWord = ({answer}: {answer: Answer}) => {
    const translateX = useSharedValue(0);
    const translateY = useSharedValue(0);
    const scale = useSharedValue(1);
    const zIndex = useSharedValue(0);

    const checkAndHandleDrop = (
      eventAbsoluteX: number,
      eventAbsoluteY: number,
    ) => {
      if (refDropZone?.current) {
        refDropZone.current.measureInWindow((x, y, width, height) => {
          const moreSize = 10;
          const wordX = eventAbsoluteX;
          const wordY = eventAbsoluteY;

          const dropZoneX = x - moreSize;
          const dropZoneY = y - moreSize;
          const dropZoneRight = x + width + moreSize;
          const dropZoneBottom = y + height + moreSize;

          if (
            wordX >= dropZoneX &&
            wordX <= dropZoneRight &&
            wordY >= dropZoneY &&
            wordY <= dropZoneBottom
          ) {
            addWordToDropZone(answer);
          }
        });
      }
    };

    const gestureHandler = useAnimatedGestureHandler({
      onStart: (_, ctx) => {
        'worklet';
        scale.value = withSpring(1.1);
        zIndex.value = 1000;
      },
      onActive: (event, ctx) => {
        'worklet';
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      },
      onEnd: event => {
        'worklet';
        runOnJS(checkAndHandleDrop)(event.absoluteX, event.absoluteY);

        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        scale.value = withSpring(1);
        zIndex.value = 0;
      },
    });

    const animatedStyle = useAnimatedStyle(() => {
      'worklet';
      return {
        transform: [
          {translateX: translateX.value},
          {translateY: translateY.value},
          {scale: scale.value},
        ],
        zIndex: zIndex.value,
      };
    });

    return (
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={[styles.wordContainer, animatedStyle]}>
          <Text style={styles.wordText}>{answer.text}</Text>
        </Animated.View>
      </PanGestureHandler>
    );
  };

  // Render con chim trên cành cây
  const getBirdBubble = (side: string) => {
    const text = 'Kéo các từ theo thứ tự tại đây';
    return side === 'left' ? (
      <View style={styles.birdBubbleContainer}>
        <View style={styles.bubbleWrapper}>
          <Image source={require('./assets/bubble_left.png')} />
          <Text style={[styles.bubbleText, styles.bubbleTextLeft]}>{text}</Text>
        </View>
        <View style={styles.birdLeft}>
          <Image source={require('./assets/bird_left.png')} />
        </View>
      </View>
    ) : (
      <View style={styles.birdBubbleRight}>
        <View style={styles.bubbleWrapper}>
          <Image source={require('./assets/bubble_right.png')} />
          <Text style={[styles.bubbleText, styles.bubbleTextRight]}>
            {text}
          </Text>
        </View>
        <View style={styles.birdRight}>
          <Image source={require('./assets/bird_right.png')} />
        </View>
      </View>
    );
  };

  // render cành cây
  const getBranch = (item: DropZone) => {
    const side = item.index % 2 === 0 ? 'left' : 'right';
    const image =
      side === 'left'
        ? require('./assets/branch_left.png')
        : require('./assets/branch_right.png');

    const isShowBird = item.index > 1 && !item.answer && item.showZone;

    return (
      <View
        style={[
          styles.branchContainer,
          {alignItems: side === 'left' ? 'flex-start' : 'flex-end'},
        ]}>
        <View style={styles.branchWrapper}>
          <ImageBackground source={image} style={styles.branchImage} />
          {isShowBird && getBirdBubble(side)}
          {(item.showZone || item.answer) && (
            <View ref={refDropZone} style={styles.dropZoneContainer}>
              <CardText text={item.answer?.text || '...'} />
            </View>
          )}
        </View>
      </View>
    );
  };

  const getText = () => {
    const list = [...listZone].filter(item => item.answer);
    return list.reverse().map((item: DropZone) => item.answer?.text + ' ');
  };
  return (
    <SafeAreaView style={styles.safeArea}>
      <ImageBackground
        source={require('./assets/background.png')}
        style={styles.backgroundImage}>
        {/* Header */}
        <View style={{marginHorizontal: 16}}>
          <HeadGame
            timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
            isShowSuggest={true}
            onUseHint={onShowModelConfirm}
          />
          <View>
            <LineProgressBar
              progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
            <View style={styles.headerTextContainer}>
              <View style={styles.headerText}>
                <Text style={styles.headerTextStyle}>{getText()}</Text>
              </View>

              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
          </View>
        </View>

        {/* Body */}
        <View style={styles.bodyContainer}>
          <FlatList
            ref={flatListRef}
            data={listZone}
            keyExtractor={item => item.id}
            renderItem={({item}) => getBranch(item)}></FlatList>
        </View>
        <View style={styles.wordsContainer}>
          {React.useMemo(() => {
            return listAnswer.map((answer: Answer) => (
              <DraggableWord key={answer.id} answer={answer} />
            ));
          }, [listAnswer])}
        </View>
        {/* <Button title="Check" onPress={}></Button> */}

        {/* Bottom */}
        <View style={styles.bottomContainer}>
          <BottomGame
            resetGame={startGame}
            backGame={() => {}}
            pauseGame={onPauseGame}
            volumeGame={() => {}}
          />
        </View>
      </ImageBackground>
      <View style={styles.modalContainer}>
        <ModelConfirm
          isShow={isShowModelConfirm}
          closeModal={() => setIsShowModelConfirm(false)}
          onConfirm={onShowHintModel}
        />
        <HintModel
          isShow={isShowHintModel}
          closeModal={() => setIsShowHintModel(false)}
          text={currentQuestion.hint}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <ModelPauseGame
          visible={isPauseGame}
          message={'Bạn đang tạm dừng trò chơi'}
          onContinue={onContinueGame}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  wordContainer: {
    margin: 6,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#32325D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wordText: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  wordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 70,
  },
  birdBubbleContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    transform: [{translateX: 30}, {translateY: -150}],
  },
  birdBubbleRight: {
    position: 'absolute',
    top: 0,
    left: 0,
    transform: [{translateX: -80}, {translateY: -150}],
  },
  bubbleWrapper: {
    width: 100,
    height: 100,
    position: 'relative',
  },
  bubbleText: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    fontSize: 13,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  bubbleTextLeft: {
    transform: [{translateX: -40}, {translateY: -35}],
  },
  bubbleTextRight: {
    transform: [{translateX: -35}, {translateY: -30}],
  },
  birdLeft: {
    transform: [{translateX: -20}],
  },
  birdRight: {
    marginLeft: 70,
  },
  branchContainer: {
    width: '100%',
    height: 100,
    alignItems: 'flex-start',
    zIndex: 1000,
  },
  branchWrapper: {
    width: '45%',
    position: 'relative',
  },
  branchImage: {
    width: '100%',
    height: 100,
  },
  dropZoneContainer: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    top: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTextContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  headerTextStyle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  bodyContainer: {
    flex: 1,
    marginTop: 20,
    marginBottom: 30,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    marginHorizontal: 16,
  },
  safeArea: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
  },
  modalContainer: {
    zIndex: 1000,
  },
});

export default StartSakuTC;
