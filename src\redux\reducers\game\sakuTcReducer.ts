import {createSlice} from '@reduxjs/toolkit';

interface Question {
  id: string;
  hint: string;
  answers: Answer[];
}

export interface Answer {
  id: string;
  text: string;
  position: number;
}

interface State {
  listQuestion: Question[];
  currentQuestion: Question;
  questionDone: number;
  totalQuestion: number;
}

const data: Question[] = [
  {
    id: '1',
    hint: 'subject',
    answers: [
      {id: '1', text: 'subject', position: 1},
      {id: '2', text: 'I', position: 2},
      {id: '3', text: 'the', position: 3},
      {id: '4', text: 'is', position: 4},
      {id: '5', text: 'saber', position: 5},
    ],
  },
  {
    id: '2',
    hint: 'He',
    answers: [
      {id: '1', text: 'the', position: 1},
      {id: '2', text: 'enough', position: 2},
      {id: '3', text: 'He', position: 3},
      {id: '4', text: 'answer', position: 4},
    ],
  },
];

const initialState: State = {
  listQuestion: [],
  currentQuestion: data[0],
  questionDone: 0,
  totalQuestion: data.length,
};

export const SakuTCReducer = createSlice({
  name: 'SakuTCReducer',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame: state => {
      state.listQuestion = data;
      state.currentQuestion = data[0];
      state.questionDone = 0;
      state.totalQuestion = data.length;
    },
  },
});

export const {setData, startGame} = SakuTCReducer.actions;

export default SakuTCReducer.reducer;
