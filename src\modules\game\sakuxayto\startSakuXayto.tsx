import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Dimensions,
  Animated,
  Easing,
  ImageBackground,
} from 'react-native';

import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useDhbcHook} from '../../../redux/hook/game/dhbcHook';
import {SafeAreaView} from 'react-native-safe-area-context';

import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';
import ModelDoneLevel from '../components/ModelDoneLevel';

import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {
  DraggableWordInSentence,
  DraggableWord,
} from './components/draggableWords';
import {InsertZone} from './components/insertView';
import {useNavigation, useRoute} from '@react-navigation/native';
import ConfigAPI from '../../../Config/ConfigAPI';
import {
  loadGameConfig,
  loadGameQuestions,
} from '../../../redux/reducers/game/sakuTBReducer';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

interface InsertZonePosition {
  x: number;
  y: number;
  index: number;
  distance?: number;
}

const SakuXayTo = () => {
  const dhbcHook = useDhbcHook();
  const gameHook = useGameHook();
  const {totalLives, currentLives, isGameOver, messageGameOver, gem, cup} =
    useSelector((state: RootState) => state.gameStore);
  const {totalQuestion, questionDone, currentQuestion, isWinLevel} =
    useSelector((state: RootState) => state.dhbcStore);
  const navigation = useNavigation<any>();

  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);

  const [sentence, setSentence] = useState(['Tôi', 'ăn', 'cơm']);
  const [draggableWords, setDraggableWords] = useState([
    'đang',
    'sẽ',
    'đã',
    'rất',
    'ngon',
  ]);
  const [usedWords, setUsedWords] = useState<Set<string>>(new Set());
  const [activeZoneIndex, setActiveZoneIndex] = useState<number | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isInserting, setIsInserting] = useState(false);
  const [insertingAtIndex, setInsertingAtIndex] = useState<number>(-1);
  const [draggingWordFromSentence, setDraggingWordFromSentence] = useState<{
    word: string;
    index: number;
    uniqueId: string; // Unique identifier for this specific word instance
  } | null>(null);
  const [currentlyDraggingWord, setCurrentlyDraggingWord] = useState<
    string | null
  >(null);

  const insertZoneRefs = useRef<View[]>([]);
  const insertZonesRef = useRef<InsertZonePosition[]>([]);
  const currentSentenceRef = useRef<string[]>(sentence);

  // Animation refs for sentence expansion
  const sentenceContainerHeight = useRef(new Animated.Value(80)).current;

  //router param
  const route = useRoute<any>();
  const {competenceId, milestoneId} = route.params || {competenceId: '1'};

  // State cho Winner Modal
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const dispatch: AppDispatch = useDispatch();

  // Load game config and questions on mount
  useEffect(() => {
    console.log('[StartSakuTB] Loading game config and questions...');

    // Load game config first
    dispatch(
      loadGameConfig({
        gameId: ConfigAPI.gameSKXT,
      }) as any,
    );

    // Load game questions
    dispatch(
      loadGameQuestions({
        gameId: ConfigAPI.gameSKXT,
        stage: milestoneId,
        competenceId: competenceId,
      }) as any,
    );
  }, [dispatch, competenceId, milestoneId]);

  // Update sentence ref when sentence changes
  React.useEffect(() => {
    currentSentenceRef.current = sentence;
  }, [sentence]);

  const updateInsertZonePositions = useCallback(() => {
    const newZones: InsertZonePosition[] = [];
    let completed = 0;

    insertZoneRefs.current.forEach((ref, index) => {
      if (ref) {
        ref.measure((_, __, width, height, pageX, pageY) => {
          // Tính toán vị trí chính xác của insert zone
          const centerX = pageX + width / 2;
          const centerY = pageY + height / 2;

          newZones[index] = {
            x: centerX,
            y: centerY,
            index,
          };
          completed++;

          if (completed === insertZoneRefs.current.length) {
            console.log('Updated insert zones:', newZones);
            insertZonesRef.current = newZones;
          }
        });
      }
    });
  }, []);

  // Cập nhật vị trí khi component mount và khi sentence thay đổi
  React.useLayoutEffect(() => {
    console.log('Layout effect running');
    // Thêm delay nhỏ để đảm bảo layout đã được cập nhật
    const timer = setTimeout(() => {
      updateInsertZonePositions();
    }, 100);
    return () => clearTimeout(timer);
  }, [sentence, updateInsertZonePositions]);

  const handleInsert = useCallback(
    (index: number, word: string) => {
      console.log('=== INSERT WORD ===');
      console.log('Word:', word);
      console.log('At index:', index);
      console.log('Current sentence:', sentence);

      if (usedWords.has(word)) {
        console.log('Word already used:', word);
        return;
      }
      if (sentence.length >= 10) {
        console.log('Sentence too long');
        return;
      }

      // Sử dụng functional update để đảm bảo cập nhật dựa trên state mới nhất
      setSentence(prevSentence => {
        const newSentence = [...prevSentence];
        newSentence.splice(index, 0, word);
        console.log('New sentence:', newSentence);
        return newSentence;
      });

      setUsedWords(prev => new Set([...prev, word]));
      setDraggableWords(prev => prev.filter(w => w !== word));
    },
    [usedWords],
  );

  const handleDragStart = useCallback(() => {
    setActiveZoneIndex(null);
  }, []);

  // Handle drag start for words in sentence
  const handleSentenceWordDragStart = useCallback(
    (word: string, index: number) => {
      console.log('🚀 DRAG START CALLED:', word, 'at index:', index);
      console.log(
        '🚀 Current currentlyDraggingWord before:',
        currentlyDraggingWord,
      );

      const uniqueId = `${Date.now()}-${Math.random()}`;
      setDraggingWordFromSentence({word, index, uniqueId});
      setCurrentlyDraggingWord(word);
      setActiveZoneIndex(null);

      console.log('🚀 Set currentlyDraggingWord to:', word);
      console.log('🚀 draggingWordFromSentence set to:', {
        word,
        index,
        uniqueId,
      });
    },
    [],
  );

  // Handle drag end for words in sentence
  const handleSentenceWordDragEnd = useCallback(
    (word: string, fromIndex: number, x: number, y: number) => {
      console.log('=== SENTENCE WORD DRAG END ===');
      console.log('Word:', word, 'FromIndex:', fromIndex);
      console.log('Drop position:', x, y);
      console.log('Current sentence before move:', currentSentenceRef.current);

      const zones = insertZonesRef.current;

      if (zones.length === 0) {
        console.log('No zones available');
        setDraggingWordFromSentence(null);
        return;
      }

      const closestZone = zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);

          // Give priority to end zone (larger detection area)
          const isEndZone = zone.index === sentence.length;
          const effectiveDistance = isEndZone ? distance * 0.8 : distance; // 20% bonus for end zone

          return effectiveDistance < (closest.distance || Infinity)
            ? {...zone, distance: effectiveDistance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );

      console.log('Closest zone:', closestZone);

      if (closestZone.distance && closestZone.distance < 100) {
        // Increased radius for easier targeting
        let targetIndex = closestZone.index;
        console.log('Target index before adjustment:', targetIndex);

        // Only move if target position is different from current position
        if (targetIndex !== fromIndex && targetIndex !== fromIndex + 1) {
          const currentSentence = currentSentenceRef.current;

          // Find the current position of the word
          let actualFromIndex = -1;

          // First, try the original index if it's still valid
          if (
            fromIndex >= 0 &&
            fromIndex < currentSentence.length &&
            currentSentence[fromIndex] === word
          ) {
            actualFromIndex = fromIndex;
          } else {
            // If original index doesn't work, find the word in the sentence
            // For duplicate words, we need a better strategy
            actualFromIndex = currentSentence.findIndex(w => w === word);

            if (actualFromIndex === -1) {
              console.error(
                'Word not found in sentence:',
                word,
                'Current sentence:',
                currentSentence,
              );
              setDraggingWordFromSentence(null);
              return;
            }
          }

          console.log(
            'Original fromIndex:',
            fromIndex,
            'Actual fromIndex:',
            actualFromIndex,
          );

          // Use the actual current position
          fromIndex = actualFromIndex;

          setSentence(prevSentence => {
            console.log('Previous sentence:', prevSentence);

            // Double check the word is still at the expected position
            if (prevSentence[fromIndex] !== word) {
              console.error(
                'Word not found at expected position during setState',
              );
              return prevSentence; // Don't change anything
            }

            const newSentence = [...prevSentence];

            // Remove word from current position
            const removedWord = newSentence.splice(fromIndex, 1)[0];
            console.log('Removed word:', removedWord);

            // Adjust target index if it's after the removed word
            if (targetIndex > fromIndex) {
              targetIndex -= 1;
            }

            // Validate target index
            if (targetIndex < 0) targetIndex = 0;
            if (targetIndex > newSentence.length)
              targetIndex = newSentence.length;

            // Insert word at new position
            newSentence.splice(targetIndex, 0, removedWord);
            console.log('Final target index:', targetIndex);
            console.log('New sentence after move:', newSentence);

            return newSentence;
          });

          // Reset drag states immediately after successful move
          console.log('✅ SUCCESSFUL MOVE - Resetting all drag states');
          setDraggingWordFromSentence(null);
          setCurrentlyDraggingWord(null);
          setActiveZoneIndex(null);
          setIsInserting(false);
          setInsertingAtIndex(-1);
          console.log('✅ All drag states reset after successful move');

          // Update insert zone positions after sentence change
          setTimeout(() => {
            updateInsertZonePositions();
          }, 100);
        } else {
          console.log('Target position same as current, no move needed');
        }
      } else {
        // Check if dragged outside sentence area - remove from sentence
        const sentenceArea = {
          x: 50,
          y: 400, // Approximate sentence area
          width: SCREEN_WIDTH - 100,
          height: 200,
        };

        if (
          x < sentenceArea.x ||
          x > sentenceArea.x + sentenceArea.width ||
          y < sentenceArea.y ||
          y > sentenceArea.y + sentenceArea.height
        ) {
          console.log('Word dragged outside sentence area, removing');

          const currentSentence = currentSentenceRef.current;

          // Find the current position of the word
          let actualFromIndex = -1;

          // First, try the original index if it's still valid
          if (
            fromIndex >= 0 &&
            fromIndex < currentSentence.length &&
            currentSentence[fromIndex] === word
          ) {
            actualFromIndex = fromIndex;
          } else {
            // If original index doesn't work, find the word in the sentence
            actualFromIndex = currentSentence.findIndex(w => w === word);

            if (actualFromIndex === -1) {
              console.error(
                'Cannot find word to remove:',
                word,
                'Current sentence:',
                currentSentence,
              );
              setDraggingWordFromSentence(null);
              return;
            }
          }

          console.log('Removing word at actualFromIndex:', actualFromIndex);

          // Validate the found index
          if (
            actualFromIndex >= 0 &&
            actualFromIndex < currentSentence.length
          ) {
            setSentence(prevSentence => {
              const newSentence = [...prevSentence];
              newSentence.splice(actualFromIndex, 1);
              console.log('Sentence after removing word:', newSentence);
              return newSentence;
            });

            setDraggableWords(prev => [...prev, word]);
            setUsedWords(prev => {
              const newUsed = new Set(prev);
              newUsed.delete(word);
              return newUsed;
            });

            // Reset drag states immediately after removal
            setDraggingWordFromSentence(null);
            setCurrentlyDraggingWord(null);
            setActiveZoneIndex(null);
            setIsInserting(false);
            setInsertingAtIndex(-1);

            // Update insert zone positions after sentence change
            setTimeout(() => {
              updateInsertZonePositions();
            }, 100);
          } else {
            console.error(
              'Cannot remove word - invalid position or word mismatch',
            );
          }
        }
      }

      // Reset states immediately for cases where no movement occurred
      if (!closestZone.distance || closestZone.distance >= 80) {
        setDraggingWordFromSentence(null);
        setCurrentlyDraggingWord(null);
        setActiveZoneIndex(null);
        setIsInserting(false);
        setInsertingAtIndex(-1);
      }

      console.log('=== END SENTENCE WORD DRAG ===');
    },
    [SCREEN_WIDTH, updateInsertZonePositions],
  );

  const handleDragMove = useCallback(
    (x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) return;

      const closestZone = zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);

          // Give priority to end zone (larger detection area)
          const isEndZone = zone.index === sentence.length;
          const effectiveDistance = isEndZone ? distance * 0.8 : distance; // 20% bonus for end zone

          return effectiveDistance < (closest.distance || Infinity)
            ? {...zone, distance: effectiveDistance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );

      // Optimized detection radius for smoother interaction
      const detectionRadius = 100; // Larger radius for easier targeting
      if (closestZone.distance && closestZone.distance < detectionRadius) {
        // If dragging a word from sentence, adjust the target index logic
        let targetIndex = closestZone.index;

        if (draggingWordFromSentence) {
          // When dragging from sentence, find the current position of the word
          const currentSentence = currentSentenceRef.current;
          const currentFromIndex = currentSentence.findIndex(
            w => w === draggingWordFromSentence.word,
          );

          if (currentFromIndex !== -1) {
            // Don't highlight the same position or adjacent positions for smoother UX
            if (
              targetIndex === currentFromIndex ||
              targetIndex === currentFromIndex + 1
            ) {
              // Smooth transition out of highlight
              if (activeZoneIndex !== null) {
                setActiveZoneIndex(null);
                setIsInserting(false);
                setInsertingAtIndex(-1);

                // Smooth container height reset
                Animated.timing(sentenceContainerHeight, {
                  toValue: 80,
                  duration: 250,
                  easing: Easing.out(Easing.cubic),
                  useNativeDriver: false,
                }).start();
              }
              return;
            }
          }
        }

        // Smooth transition to new target
        if (activeZoneIndex !== targetIndex) {
          setActiveZoneIndex(targetIndex);

          // Trigger smooth word expansion animation
          if (targetIndex <= sentence.length) {
            setIsInserting(true);
            setInsertingAtIndex(targetIndex);

            // Smooth container height expansion
            Animated.timing(sentenceContainerHeight, {
              toValue: 110, // Slightly more space for better visibility
              duration: 300, // Smoother animation
              easing: Easing.out(Easing.cubic),
              useNativeDriver: false,
            }).start();
          }
        }
      } else {
        // Smooth transition out of any highlight
        if (activeZoneIndex !== null) {
          setActiveZoneIndex(null);
          setIsInserting(false);
          setInsertingAtIndex(-1);

          // Smooth container height reset
          Animated.timing(sentenceContainerHeight, {
            toValue: 80,
            duration: 300,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: false,
          }).start();
        }
      }
    },
    [
      activeZoneIndex,
      sentence.length,
      sentenceContainerHeight,
      draggingWordFromSentence,
    ],
  );

  const handleDragEnd = useCallback(
    (word: string, x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) return;

      const closestZone = zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);
          return distance < (closest.distance || Infinity)
            ? {...zone, distance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );

      if (closestZone.distance && closestZone.distance < 50) {
        if (usedWords.has(word)) return;

        setSentence(prevSentence => {
          const newSentence = [...prevSentence];
          newSentence.splice(closestZone.index, 0, word);
          return newSentence;
        });

        setUsedWords(prev => new Set([...prev, word]));
        setDraggableWords(prev => prev.filter(w => w !== word));
      }

      setActiveZoneIndex(null);

      // Reset expansion animation after drop
      setIsInserting(false);
      setInsertingAtIndex(-1);

      // Reset sentence container height
      Animated.timing(sentenceContainerHeight, {
        toValue: 80,
        duration: 300,
        easing: Easing.out(Easing.back(1.1)),
        useNativeDriver: false,
      }).start();
    },
    [usedWords, sentenceContainerHeight],
  );

  const handleSubmit = useCallback(() => {
    setIsSubmitted(true);
  }, []);

  const resetSentence = useCallback(() => {
    setSentence(['Tôi', 'ăn', 'cơm']);
    setUsedWords(new Set());
    setDraggableWords(['đang', 'sẽ', 'đã', 'rất', 'ngon']);
    setIsSubmitted(false);

    // Reset animation states
    setIsInserting(false);
    setInsertingAtIndex(-1);
    setActiveZoneIndex(null);
    setDraggingWordFromSentence(null);
    setCurrentlyDraggingWord(null);

    // Reset sentence container height
    sentenceContainerHeight.setValue(80);
  }, [sentenceContainerHeight]);

  useEffect(() => {
    startGame();
  }, []);

  useEffect(() => {
    if (currentLives === 0) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);
  useEffect(() => {
    if (isWinLevel) {
      winGame();
    }
  }, [isWinLevel]);

  const startGame = () => {
    resetSentence();
    gameHook.restartGame();
    dhbcHook.startGame();
  };

  // Thua
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // Thắng
  const winGame = () => {
    gameHook.setData({stateName: 'gem', value: gem + 30});
    gameHook.setData({stateName: 'cup', value: cup + 10});
  };

  // Sử dụng gợi ý
  const useHint = () => {
    gameHook.setData({stateName: 'gem', value: gem - 10});
    setShowModelConfirm(false);
    setShowHintModel(true);
  };

  const currentQuestionIndex = 0;

  return (
    <ImageBackground
      source={require('./assets/backgroundGame.png')}
      style={styles.backgroundImage}
      resizeMode="cover">
      <SafeAreaView edges={['top']} />

      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          gameId={ConfigAPI.gameSKXT}
          isShowSuggest={true}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
        />

        <View>
          <LineProgressBar
            progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
          <View
            style={{
              width: '100%',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Lives totalLives={totalLives} currentLives={currentLives}></Lives>
            <CountBadge
              current={currentQuestionIndex + 1}
              total={totalQuestion}></CountBadge>
          </View>
        </View>

        {/* Game Content */}
        <View style={styles.gameContent}>
          <View style={styles.questionContainer}>
            <Text style={styles.text}>{currentQuestion?.text}</Text>
          </View>
          <GestureHandlerRootView style={{flex: 1}}>
            <View style={styles.container}>
              <View style={styles.mainContent}>
                <Animated.View style={[styles.plateContainer]}>
                  <ImageBackground
                    source={require('./assets/plate.png')}
                    style={styles.plateBackground}
                    resizeMode="stretch">
                    <View style={styles.row}>
                      {sentence.map((word, index) => {
                        const wordId = `${word}-${index}`;
                        return (
                          <View key={`slot-${index}`} style={styles.slotWrap}>
                            <InsertZone
                              ref={ref => {
                                if (ref) {
                                  insertZoneRefs.current[index] = ref;
                                }
                              }}
                              index={index}
                              onReceiveDragDrop={word =>
                                handleInsert(index, word)
                              }
                              isActive={activeZoneIndex === index}
                              onDropSuccess={() =>
                                console.log(`Word dropped at index ${index}`)
                              }
                            />
                            <DraggableWordInSentence
                              word={word}
                              index={index}
                              isSubmitted={isSubmitted}
                              isInserting={isInserting}
                              insertIndex={insertingAtIndex}
                              onDragStart={handleSentenceWordDragStart}
                              onDragMove={handleDragMove}
                              onDragEnd={handleSentenceWordDragEnd}
                              isDragging={(() => {
                                const isDrag = currentlyDraggingWord === word;
                                if (currentlyDraggingWord !== null) {
                                  console.log(
                                    `🎯 Word "${word}" at index ${index}: isDragging=${isDrag}, currentlyDraggingWord="${currentlyDraggingWord}"`,
                                  );
                                }
                                return isDrag;
                              })()}
                              dragId={wordId}
                            />
                          </View>
                        );
                      })}
                      <InsertZone
                        ref={ref => {
                          if (ref) {
                            insertZoneRefs.current[sentence.length] = ref;
                          }
                        }}
                        index={sentence.length}
                        onReceiveDragDrop={word =>
                          handleInsert(sentence.length, word)
                        }
                        isActive={activeZoneIndex === sentence.length}
                        isEndZone={true}
                        onDropSuccess={() => console.log(`Word dropped at end`)}
                      />
                    </View>
                  </ImageBackground>
                </Animated.View>
                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={[styles.button, styles.submitButton]}
                    onPress={handleSubmit}
                    disabled={isSubmitted}>
                    <Text style={styles.buttonText}>Kiểm tra đáp án</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.wordBank}>
                  {draggableWords.map((word, index) => (
                    <DraggableWord
                      key={`drag-${index}-${word}`}
                      word={word}
                      used={usedWords.has(word)}
                      onDragEnd={handleDragEnd}
                      onDragStart={handleDragStart}
                      onDragMove={handleDragMove}
                    />
                  ))}
                </View>
              </View>
            </View>
          </GestureHandlerRootView>
          <BottomGame
            resetGame={resetSentence}
            backGame={() => {
              navigation.goBack();
            }}
            pauseGame={() => {}}
            volumeGame={() => {}}
          />
        </View>
        <View style={{zIndex: 1000}}>
          <ModelConfirm
            isShow={showModelConfirm}
            closeModal={() => setShowModelConfirm(false)}
            onConfirm={useHint}
          />
          <HintModel
            isShow={showHintModel}
            closeModal={() => setShowHintModel(false)}
            text={currentQuestion.hint}
          />
          <GameOverModal
            visible={isGameOver}
            onClose={() => {}}
            restartGame={startGame}
            message={messageGameOver}
            isTimeOut={false}
          />
          <ModelDoneLevel
            visible={isWinLevel}
            onNextLevel={startGame}
            currentGem={gem - 30}
            currentCup={cup - 10}
            gemAdd={30}
            cupAdd={10}
          />
        </View>
      </View>
      <SafeAreaView edges={['bottom']} />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
  },
  mainContent: {
    flex: 1,
    width: '100%',
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
  questionContainer: {
    width: Dimensions.get('window').width - 32,
    height: undefined,
    minHeight: 65,
    marginBottom: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    backgroundColor: '#FCF8E8',
    justifyContent: 'center',
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  plateContainer: {
    width: Dimensions.get('window').width - 32,
    height: 200,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  plateBackground: {
    width: Dimensions.get('window').width - 32,
    height: 200,
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    padding: 16,
  },
  slotWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    flexShrink: 0,
  },
  wordBank: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
    padding: 10,
    width: '100%',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingVertical: 16,
    paddingHorizontal: 16,
    width: '100%',
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minWidth: 40,
  },
  submitButton: {
    backgroundColor: '#AE2B26',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  gameContent: {
    marginTop: 16,
    flex: 1,
    alignItems: 'center',
  },
});

export default SakuXayTo;
