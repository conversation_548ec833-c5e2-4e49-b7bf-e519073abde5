import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
  ImageBackground,
} from 'react-native';

const {width, height} = Dimensions.get('window');

interface GameOverModalProps {
  visible: boolean;
  message: string;
  onContinue: () => void;
}

const GameOverModal = ({visible, message, onContinue}: GameOverModalProps) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onContinue}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Hình ảnh con chim buồn */}
          <View style={styles.birdContainer}>
            <Image source={require('../ailatrieuphu/assets/lose-icon.png')} />
            <ImageBackground
              source={require('../ailatrieuphu/assets/speech-bubble.png')}
              style={styles.speechBubble}>
              <Text style={styles.messageText}>{message}</Text>
            </ImageBackground>
          </View>
          {/* Nút Next */}
          <TouchableOpacity style={styles.restartButton} onPress={onContinue}>
            <Image
              source={require('../assets/next_button.png')}
              style={styles.restartButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.69)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    width: width * 0.93,
    height: height * 0.76,
    backgroundColor: 'rgba(112, 90, 64, 0.96)', // Màu nền nâu như trong ảnh
    padding: 20,
    marginBottom: 20,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },

  birdContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginTop: 150,
    marginRight: 120,
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    height: '100%',
  },
  speechBubble: {
    position: 'absolute',
    top: -100,
    right: -80,
    borderRadius: 20,
    padding: 15,
    width: 210,
    height: 159,
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    fontWeight: 'bold',
    lineHeight: 20,
    maxWidth: 180,
    fontFamily: 'BagelFatOne-Regular',
    marginTop: 35,
  },
  restartButton: {
    width: 230,
    height: 90,
  },
});

export default GameOverModal;
