/* eslint-disable react-native/no-inline-styles */
import React, {useState, useEffect, useRef} from 'react';
import {
  ScrollView,
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  Linking,
  Dimensions,
  ViewStyle,
  ActivityIndicator,
  Image,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {
  AppButton,
  FBottomSheet,
  FLoading,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import {useScrollProgress} from '../../../redux/hook/useScrollProgress';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import WebView from 'react-native-webview';
import {BaseDA} from '../../../base/BaseDA';
import EmptyPage from '../../../Screen/emptyPage';
import {Pulse, Bounce} from 'react-native-animated-spinkit';
import PDFViewer from '../components/PDFViewer';

export default function DocumentTab({lessonData, step, isActive}: any) {
  const [contentHeight, setContentHeight] = useState(0);
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  const [documents, setDocuments] = useState<any[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'pdf'>('pdf'); // Default to PDF view
  const [currentDocumentIndex, setCurrentDocumentIndex] = useState(0); // Track current viewing document
  const bottomSheetRef = useRef<any>(null);
  useEffect(() => {
    if (lessonData && lessonData.Document) {
      try {
        const getFileInfo = async () => {
          // kiểm tra lessonData.Document có thể có 1 id hoặc nhiều hơn 1 id của document dựa vào dấu , trong document
          const ids = lessonData.Document.split(',');
          const fileInfo = await BaseDA.getFilesInfor(ids);
          if (fileInfo) {
            setDocuments(
              fileInfo.data.map((item: any) => ({
                name: item.Name,
                url: item.Url,
                ...item,
              })),
            );
          }
        };
        getFileInfo();

        // If Document is a string that contains JSON
        // if (typeof lessonData.Document === 'string') {
        //   const parsedDocs = JSON.parse(lessonData.Document);
        //   if (Array.isArray(parsedDocs)) {
        //     setDocuments(parsedDocs);
        //   } else {
        //     // If it's a single document object
        //     setDocuments([parsedDocs]);
        //   }
        // } else if (Array.isArray(lessonData.Document)) {
        //   // If Document is already an array
        //   setDocuments(lessonData.Document);
        // } else if (typeof lessonData.Document === 'object') {
        //   // If Document is a single object
        //   setDocuments([lessonData.Document]);
        // }
      } catch (error) {
        // console.error('Error parsing document data:', error);
        // If parsing fails, try to use it as a single document
        // setDocuments([{name: 'Tài liệu', url: lessonData.Document}]);
      }
    }
  }, [lessonData]);

  const handleScroll = useScrollProgress({
    step,
    contentHeight,
    scrollViewHeight,
    isActive,
  });

  const isScrollEnabled = contentHeight > scrollViewHeight;
  const {width, height} = Dimensions.get('window');

  // Calculate available height for PDF viewer
  const getAvailablePDFHeight = () => {
    const statusBarHeight = 50; // Approximate status bar height
    const headerHeight = 60; // Header with lesson name
    const tabBarHeight = 50; // Tab bar height
    const videoPlayerHeight = 200; // Video player height
    const videoNavigationHeight = 60; // Video navigation bar height
    const floatingButtonSpace = 100; // Space for floating button
    const padding = 32; // Top and bottom padding

    const calculatedHeight =
      height -
      statusBarHeight -
      headerHeight -
      tabBarHeight -
      videoPlayerHeight -
      videoNavigationHeight -
      floatingButtonSpace -
      padding;
    const minHeight = 250; // Minimum height to ensure PDF is visible

    return Math.max(calculatedHeight, minHeight);
  };

  const downloadDocument = (url: string) => {
    if (url) {
      Linking.openURL(ConfigAPI.url.replace('/api/', '') + url).catch(err => {
        console.error('Error downloading document:', err);
      });
    }
  };

  // Helper function to check if file is PDF
  const isPDFFile = (url: string): boolean => {
    return url.toLowerCase().includes('.pdf');
  };

  // Get the current document for PDF viewing
  const currentDocument =
    documents.length > 0 ? documents[currentDocumentIndex] : null;
  const canShowPDF = currentDocument && isPDFFile(currentDocument.url);

  // Debug logging
  console.log('DocumentTab Debug:');
  console.log('- viewMode:', viewMode);
  console.log('- currentDocumentIndex:', currentDocumentIndex);
  console.log('- documents.length:', documents.length);
  console.log('- currentDocument:', currentDocument?.name);
  console.log('- canShowPDF:', canShowPDF);
  console.log('- screen height:', height);
  console.log('- calculated PDF height:', getAvailablePDFHeight());

  // Handle document selection from list
  const selectDocument = (index: number) => {
    console.log('Selecting document:', index, documents[index]?.name);

    // If selecting the same document, just switch to PDF view
    if (index === currentDocumentIndex) {
      setViewMode('pdf');
      return;
    }

    // For different document, update index and switch to PDF view
    setCurrentDocumentIndex(index);
    setViewMode('pdf');
  };

  // Force re-render when currentDocumentIndex changes
  useEffect(() => {
    console.log('currentDocumentIndex changed to:', currentDocumentIndex);
    if (viewMode === 'pdf' && documents.length > 0) {
      console.log(
        'Should show PDF for document:',
        documents[currentDocumentIndex]?.name,
      );
    }
  }, [currentDocumentIndex, viewMode, documents]);

  return (
    <View style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />

      {/* Floating ordered-list button - only show in PDF view */}
      {documents.length > 0 && canShowPDF && viewMode === 'pdf' && (
        <AppButton
          containerStyle={styles.floatingButton}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          title={
            <Winicon
              src="outline/text/ordered-list"
              size={24}
              color={ColorThemes.light.Neutral_Background_Color_Absolute}
            />
          }
          onPress={() => setViewMode('list')}
        />
      )}

      {/* Content */}
      {documents.length > 0 ? (
        viewMode === 'pdf' && canShowPDF ? (
          // PDF View
          <View style={styles.pdfViewContainer}>
            <PDFViewer
              key={`pdf-viewer-${currentDocumentIndex}`}
              url={currentDocument.url}
              fileName={currentDocument.name || 'Tài liệu PDF'}
              height={getAvailablePDFHeight()} // Re-enable dynamic height
              maxFileSize={15}
              enableOptimization={true} // Re-enable optimization
              useGoogleViewer={true}
              onError={error => {
                console.error('PDF Viewer error:', error);
                console.error('Failed URL:', currentDocument.url);
                // Fallback to list view if PDF fails to load
                setViewMode('list');
              }}
              onLoadStart={() => {
                console.log('PDF loading started for:', currentDocument.name);
              }}
              onLoadEnd={() => {
                console.log('PDF loading completed for:', currentDocument.name);
              }}
            />
          </View>
        ) : (
          // List View
          <ScrollView
            style={{flex: 1}}
            scrollEnabled={isScrollEnabled}
            onContentSizeChange={(w, h) => setContentHeight(h)}
            onLayout={e => setScrollViewHeight(e.nativeEvent.layout.height)}
            contentContainerStyle={{
              paddingHorizontal: 16,
              gap: 16,
              paddingTop: 16,
            }}
            onScroll={handleScroll}
            scrollEventThrottle={16}>
            {documents.map((item: any, index: number) => {
              const isCurrentDocument = index === currentDocumentIndex;
              return (
                <View
                  key={index}
                  style={[
                    styles.documentItem,
                    isCurrentDocument && styles.documentItemActive,
                  ]}>
                  <ListTile
                    onPress={() => {
                      console.log('🔥 ListTile pressed for index:', index);
                      console.log('🔥 Document name:', item.name);
                      selectDocument(index);
                    }}
                    leading={
                      <View
                        style={[
                          styles.documentIconContainer,
                          isCurrentDocument &&
                            styles.documentIconContainerActive,
                        ]}>
                        <Winicon
                          src="fill/files/document-2"
                          size={24}
                          color={
                            isCurrentDocument
                              ? ColorThemes.light
                                  .Neutral_Background_Color_Absolute
                              : ColorThemes.light.Primary_Color_Main
                          }
                        />
                        {isCurrentDocument && (
                          <View style={styles.currentIndicator}>
                            <Winicon
                              src="outline/user interface/check"
                              size={12}
                              color={ColorThemes.light.Success_Color_Main}
                            />
                          </View>
                        )}
                      </View>
                    }
                    listtileStyle={
                      isCurrentDocument
                        ? {...styles.listTile, ...styles.listTileActive}
                        : styles.listTile
                    }
                    leadingContainer={styles.leadingContainer}
                    title={item.name || `Tài liệu ${index + 1}`}
                    subtitle={
                      isCurrentDocument
                        ? 'Đang xem tài liệu này - Nhấn để xem lại'
                        : item.description || 'Nhấn để xem tài liệu'
                    }
                    trailing={
                      <View style={styles.actionContainer}>
                        <TouchableOpacity
                          style={styles.actionButton}
                          onPress={e => {
                            e.stopPropagation();
                            console.log(
                              '🔥 Download button pressed for:',
                              item.name,
                            );
                            downloadDocument(item.url);
                          }}>
                          <Winicon
                            src="outline/user interface/data-download"
                            size={20}
                          />
                        </TouchableOpacity>
                      </View>
                    }
                  />
                </View>
              );
            })}
          </ScrollView>
        )
      ) : (
        <View style={styles.emptyContainer}>
          <Winicon
            src="outline/files/document"
            size={48}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
          <Text style={styles.emptyText}>Không có tài liệu nào</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  headerTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: ColorThemes.light.Primary_Color_Main,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  toggleButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Primary_Color_Main,
    marginLeft: 4,
  },
  floatingButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    zIndex: 1000,
    bottom: 20,
    right: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  pdfViewContainer: {
    flex: 1,
    width: '100%', // Full width
    paddingHorizontal: 16, // Side padding
    paddingTop: 16, // Top padding
    paddingBottom: 100, // Space for floating button and bottom controls
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  documentItem: {
    borderRadius: 8,
    marginBottom: 8,
    overflow: 'hidden',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  documentItemActive: {
    backgroundColor: ColorThemes.light.primary_light_color,
    borderWidth: 2,
    borderColor: ColorThemes.light.Primary_Color_Main,
  },
  documentIconContainer: {
    position: 'relative',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.primary_light_color,
  },
  documentIconContainerActive: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
  currentIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.Success_Color_Main,
  },
  listTile: {
    gap: 16,
    marginBottom: 8,
    paddingBottom: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  listTileActive: {
    backgroundColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  leadingContainer: {
    marginRight: 12, // Add extra space between icon and text
  },
  actionContainer: {
    gap: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
    borderWidth: 1,
    borderRadius: 100,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    gap: 16,
  },
  emptyText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    textAlign: 'center',
  },
});

export const LoadingUI = ({style}: {style?: ViewStyle}) => {
  return (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
        height: Dimensions.get('window').height,
        width: '100%',
        opacity: 0.8,
        ...style,
      }}
      pointerEvents="none">
      <Pulse
        size={150}
        color={ColorThemes.light.Primary_Color_Main}
        style={{position: 'absolute'}}></Pulse>
      <Bounce
        size={80}
        color={ColorThemes.light.Primary_Color_Main}
        style={{position: 'absolute'}}></Bounce>
      <ActivityIndicator
        size={60}
        color={ColorThemes.light.Primary_Color_Main}
        style={{position: 'absolute'}}
      />
      <Image
        style={{width: 50, height: 50, borderRadius: 25, position: 'absolute'}}
        source={require('../../../assets/appstore.png')}
      />
    </View>
  );
};
